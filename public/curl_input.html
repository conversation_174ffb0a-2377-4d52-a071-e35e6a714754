<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zillow - Powerful API: cURL Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        /* Navbar Styling */
        .navbar {
            background-color: #121c24;
            padding: 10px 0;
            text-align: center;
            border-bottom: 1px solid #ccc; /* Add a subtle border */
        }

        .navbar img {
            max-height: 50px;
        }

        /* Main Content Styling */
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff; /* Light background for better contrast */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Subtle shadow */
        }

        /* Input and Output Styling */
        .input-group, .result-group, .output {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
        }

        .input-group input, .input-group textarea {
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            border: 1px solid #ccc; /* Add a border */
            border-radius: 4px;   /* Slightly rounded corners */
        }

        .input-group button {
            padding: 10px 20px;
            background-color: #007bff;
            color: #fff;
            border: none;
            cursor: pointer;
        }

        .input-group button:hover {
            background-color: #0056b3;
        }

        .loading-icon {
            display: none;
            text-align: center;
            font-weight: bold;
        }

        #output { /* Style for the JSON output area */
            font-family: monospace; /* Use a monospace font for better readability */
            white-space: pre; /* Preserve whitespace (for indentation) */
            overflow-x: auto; /* Add horizontal scrollbar if needed */
            padding: 15px;
            background-color: #f5f5f5; /* Light background for better contrast */
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="navbar">
        <img src="/byte-media-logo-v2.png" alt="Byte Media Logo">
    </div>
    <div class="container">
        <h1>cURL Input</h1>
        <p>cURL allows you to effortlessly request and explore API's endpoints with JSON outputs.</p>

        <p>Note: You will need an API key. We offer 250 monthly requests for free. <br>
        Sign-Up: <a href="https://rapidapi.com/brycebayens/api/zillow-powerful-api-search/pricing" target="_blank">https://rapidapi.com/brycebayens/api/zillow-powerful-api-search/pricing</a></p>
        <div class="input-group">
            <label for="curlCommand">Enter Curl Command:</label>
            <textarea rows="15" id="curlCommand" placeholder="curl ..."></textarea>
        </div>

        <div class="input-group">
            <button onclick="executeCurl()">Execute Curl</button>
            <div class="loading-icon" id="loadingIcon">Loading...</div>
        </div>
        <div class="output" id="output"></div>
    </div>
    <script>
        async function executeCurl() {
            const curlCommand = document.getElementById('curlCommand').value;

            const urlRegex = /--url '([^']+)'/;
            const hostRegex = /--header 'x-rapidapi-host: ([^']+)'/;
            const keyRegex = /--header 'x-rapidapi-key: ([^']+)'/;

            const urlMatch = curlCommand.match(urlRegex);
            const hostMatch = curlCommand.match(hostRegex);
            const keyMatch = curlCommand.match(keyRegex);

            if (!urlMatch || !hostMatch || !keyMatch) {
                document.getElementById('output').innerText = 'Error: Invalid cURL command';
                return;
            }

            const url = urlMatch[1];
            const apiUrl = decodeURIComponent(url);
            const apiHost = hostMatch[1];
            const apiKey = keyMatch[1];

            const options = {
                method: 'GET',
                headers: {
                    'x-rapidapi-key': apiKey,
                    'x-rapidapi-host': apiHost
                }
            };

            const loadingIcon = document.getElementById('loadingIcon');
            loadingIcon.style.display = 'block';
            try {
                const response = await fetch(apiUrl, options);
                const result = await response.json();

                //document.getElementById('output').innerText = JSON.stringify(result, null, 2);
                 // Improved JSON formatting with indentation
                 document.getElementById('output').innerText = JSON.stringify(result, null, 4); 
            } catch (error) {
                console.error(error);
                document.getElementById('output').innerText = 'Error: ' + error.message;
            } finally {
                loadingIcon.style.display = 'none';
            }
        }
    </script>
</body>
</html>
