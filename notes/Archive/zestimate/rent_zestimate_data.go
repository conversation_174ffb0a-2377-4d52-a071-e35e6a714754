package zestimate

import (
    "bytes"
    "encoding/json"
    "fmt"
    "io/ioutil"
    "net/http"
    "time"
)

func zestimateGo(address string) (RentZestimateResponse, error) {
    apiURL := "https://www.zillow.com/rental-manager/growth/growth-proxy/rental-growth-api/api/graphql/v1/listing"

    payload := map[string]interface{}{
        "query": fmt.Sprintf(`
        {
            byAddress(
                fullAddress: "%s"
                unit: ""
            ) {
                geo {
                    lat
                    lon
                }
                floorplans {
                    zpid
                    numBeds
                    numFullBaths
                    zestimate {
                        rentZestimate
                        rentZestimateRangeLow
                        rentZestimateRangeHigh
                    }
                    minSqft
                    maxSqft
                    address {
                        street
                        city
                        state
                        zip
                        unit
                    }
                }
                marketSummary {
                    url
                    areaName
                    beds
                    summary {
                        medianRent
                        monthlyChange
                        yearlyChange
                        avgDaysOnMarket
                        availableRentals
                    }
                }
                similarFloorplans (
                    filter: {
                        orderBy: "lowPrice",
                    }
                ) {
                    listingRanking
                    highestPriceIncluded
                    lowestPriceIncluded
                    boundingBox{
                        minLat
                        maxLat
                        minLon
                        maxLon
                    }
                    floorplans{
                        aliasEncoded
                        floorplanGroupId
                        active
                        zpid
                        numBeds
                        numFullBaths
                        numPartialBaths
                        numUnitsAvailable
                        propertyUrl
                        lowPrice
                        highPrice
                        distanceInMiles
                        minSqft
                        maxSqft
                        address{
                            street
                        }
                        geo{
                            lat
                            lon
                            quad
                        }
                        amenities{
                            laundry{
                                inUnit
                                shared
                            }
                            parking{
                                valet
                                garage
                                offStreet
                            }
                            hvac{
                                airConditioning
                                heating
                            }
                            pets{
                                cats
                                largeDogs
                                smallDogs
                            }
                        }
                        history{
                            lastUpdated{
                                date
                                ago
                            }
                            deactivated{
                                date
                                ago
                            }
                        }
                        photos{
                            listingPhotos{
                                medium
                            }
                        }
                    }
                }
            }
        }`, address),
    }

    jsonPayload, err := json.Marshal(payload)
    if err != nil {
        return RentZestimateResponse{}, err
    }

    req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonPayload))
    if err != nil {
        return RentZestimateResponse{}, err
    }

    req.Header.Add("Accept", "application/json")
    req.Header.Add("Accept-Language", "en-US,en;q=0.9")
    req.Header.Add("Cache-Control", "no-cache")
    req.Header.Add("Content-Type", "text/plain")
    req.Header.Add("Pragma", "no-cache")
    req.Header.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
    req.Header.Add("Sec-Ch-Ua", `"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"`)
    req.Header.Add("Sec-Ch-Ua-Mobile", "?0")
    req.Header.Add("Sec-Ch-Ua-Platform", `"macOS"`)
    req.Header.Add("Sec-Fetch-Dest", "empty")
    req.Header.Add("Sec-Fetch-Mode", "cors")
    req.Header.Add("Sec-Fetch-Site", "same-origin")

    client := &http.Client{
        Timeout: time.Minute,
    }
    resp, err := client.Do(req)
    if err != nil {
        return RentZestimateResponse{}, err
    }
    defer resp.Body.Close()

    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return RentZestimateResponse{}, err
    }

    if resp.StatusCode != http.StatusOK {
        return RentZestimateResponse{}, fmt.Errorf("status: %d headers: %v body: %s", resp.StatusCode, resp.Header, string(body))
    }

    var rentZestimateResponse RentZestimateResponse
    if err := json.Unmarshal(body, &rentZestimateResponse); err != nil {
        return RentZestimateResponse{}, err
    }

    return rentZestimateResponse, nil
}
