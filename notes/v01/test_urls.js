const axios = require('axios');

const ids = [
    // Add your list of property IDs here
    47554271,
    47554327,
    90167095,
    47552394,
    47538704,
    47536997,
    47554344,
    47554980,
    47539066,
    47537640,
    242998244,
    47551041,
    340042158,
    70423614,
    47538764,
    47538902,
    54265454,
    47536013,
    54265466,
    47536409,
    305876311,
    47538583,
    338551409,
    47537654,
    47538632,
    47536289,
    339884661,
    55809497,
    47538856,
    47539201,
    346288866,
    47537954,
    47537733,
    172141719,
    47552129,
    347534326,
    47535758,
    47549799,
    337159893,
    337168632,
    47536381,
    66780389,
    47536578,
    47549684,
    47537135,
    47537792,
    47537024,
    250946099,
    47536268,
    250946235,
    47549720
    // ...
];

async function testIds() {
    for (const id of ids) {
        try {
            const response = await axios.get(`http://localhost:3000/api/scrape-zillow-id`, {
                params: { id },
                timeout: 30000  // Increase the timeout to 30 seconds
            });
            console.log(`Success: ${id}`);
        } catch (error) {
            console.error(`Error with ID: ${id}`);
            if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
            } else if (error.request) {
                console.error('No response received:', error.request);
            } else {
                console.error('Error message:', error.message);
            }
        }
    }
}

testIds();
