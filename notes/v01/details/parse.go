package details

import (
	"bytes"
	"encoding/json"
	"errors"
	"html"

	"github.com/PuerkitoBio/goquery"
	"github.com/brycebayens/byte-media-api-1.0/utils"
)

func ParseBodyDetails(body []byte) (PropertyInfo, error) {
	dataRaw, err := parseBodyDetails(body)
	if err != nil {
		return PropertyInfo{}, err
	}
	return dataRaw, nil
}

func parseBodyDetails(body []byte) (PropertyInfo, error) {
	reader := bytes.NewReader(body)
	doc, err := goquery.NewDocumentFromReader(reader)
	if err != nil {
		return PropertyInfo{}, err
	}
	htmlData, err := doc.Find("#__NEXT_DATA__").Html()
	if err != nil {
		return PropertyInfo{}, err
	}
	htmlData = utils.RemoveSpace(html.UnescapeString(htmlData))
	var data bodyResponse
	if err := json.Unmarshal([]byte(htmlData), &data); err != nil {
		return PropertyInfo{}, err
	}

	var gdpClientCacheStr string
	if len(data.Props.PageProps.ComponentProps.GdpClientCache) == 1 {
		gdpClientCacheStr = data.Props.PageProps.ComponentProps.GdpClientCache[0]
	} else {
		// Handle cases where GdpClientCache might have multiple values
		return PropertyInfo{}, errors.New("Unexpected multiple values in GdpClientCache")
	}

	mapData := make(map[string]property)
	if err := json.Unmarshal([]byte(gdpClientCacheStr), &mapData); err != nil {
		return PropertyInfo{}, err
	}
	for _, property := range mapData {
		return property.Property, nil
	}
	return PropertyInfo{}, errors.New("Empty result")
}
