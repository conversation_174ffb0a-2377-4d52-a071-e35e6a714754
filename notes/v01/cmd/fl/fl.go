package main

import (
    "encoding/json"
    "fmt"
    "github.com/brycebayens/byte-media-api-1.0/details"
    "log"
    "os"
)

func main() {
    // Check if a property URL was provided as an argument
    if len(os.Args) < 2 {
        fmt.Println("Property URL is required")
        return
    }
    propertyURL := os.Args[1]

    // Retrieve property details using the URL
    property, err := details.FromPropertyURL(propertyURL, nil)
    if err != nil {
        log.Printf("Error retrieving property details: %v\n", err)
        fmt.Printf(`{"error":"%v"}`, err)
        return
    }

    // Marshal the property details into JSON
    rawJSON, err := json.MarshalIndent(property, "", "  ")
    if err != nil {
        log.Printf("Error marshalling property details: %v\n", err)
        return
    }

    // Print the JSON to standard output
    fmt.Printf("%s", rawJSON)
}
