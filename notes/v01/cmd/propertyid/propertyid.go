package main

import (
    "encoding/json"
    "fmt"
    "github.com/brycebayens/byte-media-api-1.0/details"
    "log"
    "os"
    "strconv"
)

func main() {
    // Check if a property ID was provided as an argument
    if len(os.Args) < 2 {
        fmt.Println("Property ID is required")
        return
    }
    propertyIDStr := os.Args[1]

    // Convert property ID to int64
    propertyID, err := strconv.ParseInt(propertyIDStr, 10, 64)
    if err != nil {
        log.Printf("Error parsing property ID: %v\n", err)
        fmt.Printf(`{"error":"%v"}`, err)
        return
    }

    // Retrieve property details using the property ID
    property, err := details.FromPropertyID(propertyID, nil)
    if err != nil {
        log.Printf("Error retrieving property details: %v\n", err)
        fmt.Printf(`{"error":"%v"}`, err)
        return
    }

    // Marshal the property details into JSON
    rawJSON, err := json.MarshalIndent(property, "", "  ")
    if err != nil {
        log.Printf("Error marshalling property details: %v\n", err)
        return
    }

    // Print the JSON to standard output
    fmt.Printf("%s", rawJSON)
}
