package main

import (
    "encoding/json"
    "fmt"
    "log"
    "os"
    "github.com/brycebayens/byte-media-api-1.0/search"
)

func main() {
    if len(os.Args) < 3 {
        log.Println("City and State are required")
        return
    }

    city := os.Args[1]
    state := os.Args[2]

    coords, err := search.GetCoordinates(city, state)
    if err != nil {
        log.Println("Error fetching coordinates:", err)
        fmt.Printf(`{"error":"%v"}`, err)
        return
    }

    zoomValue := 2
    pagination := 1
    results, mapResults, err := search.ForSale(pagination, zoomValue, coords.Ne.Latitude, coords.Ne.Longitud, coords.Sw.Latitude, coords.Sw.Longitud, nil)
    if err != nil {
        log.Println("Error:", err)
        fmt.Printf(`{"error":"%v"}`, err)
        return
    }

    fullResult := struct {
        Results    []search.ListResult `json:"results"`
        MapResults []search.MapResult  `json:"mapResults"`
    }{
        Results:    results,
        MapResults: mapResults,
    }

    rawJSON, err := json.MarshalIndent(fullResult, "", "  ")
    if err != nil {
        log.Println("Error marshalling JSON:", err)
        fmt.Printf(`{"error":"%v"}`, err)
        return
    }

    fmt.Printf("%s\n", rawJSON)
}
