#!/bin/bash

set -e

# Download and Install Go 
mkdir -p go_install  
wget https://go.dev/dl/go1.20.4.linux-amd64.tar.gz -O go1.20.4.linux-amd64.tar.gz  
tar -C go_install -xzf go1.20.4.linux-amd64.tar.gz

# Set Go Environment Variables
export GOROOT=$PWD/go_install/go
export GOPATH=$HOME/go
export PATH=$GOROOT/bin:$GOPATH/bin:$PATH
export GO111MODULE=on

# Build Go binaries:
go build -o bin/fl ./cmd/fl/fl.go
go build -o bin/propertyid ./cmd/propertyid/propertyid.go
go build -o bin/propertyaddress ./cmd/propertyaddress/propertyaddress.go

# Build new endpoints
go build -o bin/searchforsale ./cmd/searchforsale/searchforsale.go


# Build Go binaries Minimal:
# go build -o bin/fl-minimal ./cmd/fl/fl-minimal.go
# go build -o bin/propertyid-minimal ./cmd/propertyid/propertyid-minimal.go
# go build -o bin/propertyaddress-minimal ./cmd/propertyaddress/propertyaddress-minimal.go

# Install Node.js dependencies
npm install --production 
