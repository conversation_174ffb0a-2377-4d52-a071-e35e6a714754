package search

import (
    "encoding/json"
    "fmt"
    "net/http"
    "net/url"
    "io/ioutil"
)

type GeocodeResponse struct {
    Results []struct {
        Geometry struct {
            Location struct {
                Lat float64 `json:"lat"`
                Lng float64 `json:"lng"`
            } `json:"location"`
        } `json:"geometry"`
    } `json:"results"`
    Status string `json:"status"`
}

type CoordinatesInput struct {
    Sw CoordinatesValues
    Ne CoordinatesValues
}

type CoordinatesValues struct {
    Latitude  float64
    Longitud  float64
}

func GetCoordinates(city, state string) (CoordinatesInput, error) {
    address := fmt.Sprintf("%s, %s", city, state)
    apiKey := "AIzaSyBRJxoN7JRMqe737aSjSc6os6ourK3dPpc"
    requestURL := fmt.Sprintf("https://maps.googleapis.com/maps/api/geocode/json?address=%s&key=%s", url.QueryEscape(address), apiKey)

    resp, err := http.Get(requestURL)
    if err != nil {
        return CoordinatesInput{}, err
    }
    defer resp.Body.Close()

    body, err := ioutil.ReadAll(resp.Body)
    if err != nil {
        return CoordinatesInput{}, err
    }

    var geocodeResponse GeocodeResponse
    if err := json.Unmarshal(body, &geocodeResponse); err != nil {
        return CoordinatesInput{}, err
    }

    if geocodeResponse.Status != "OK" || len(geocodeResponse.Results) == 0 {
        return CoordinatesInput{}, fmt.Errorf("no results found")
    }

    lat := geocodeResponse.Results[0].Geometry.Location.Lat
    lng := geocodeResponse.Results[0].Geometry.Location.Lng

    coords := CoordinatesInput{
        Sw: CoordinatesValues{
            Latitude: lat - 0.05,
            Longitud: lng - 0.05,
        },
        Ne: CoordinatesValues{
            Latitude: lat + 0.05,
            Longitud: lng + 0.05,
        },
    }

    return coords, nil
}
