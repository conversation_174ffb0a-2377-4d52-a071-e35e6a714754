const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser'); // Add this line
const app = express();
const { spawn } = require('child_process');
const path = require('path');
const validator = require('validator');  
const port = process.env.PORT || 3000;

/* internal notes
lsof -i :3000                                    
kill -9
git add .
git commit -m ""
git push origin main
*/

// Use body-parser middleware
app.use(bodyParser.json()); // Add this line

// Health Check Endpoint (Placed before RapidAPI middleware)
app.get('/', (req, res) => { 
    // You can add more checks here (e.g., database connection)
    res.status(200).json({ status: 'ok' });
});

// Explicit route for favicon at the root path
app.get('/favicon.ico', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'favicon.ico'));
  });

// Explicit route for favicon at the root path
app.get('/api-logo.png', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'api-logo.png'));
});

// Explicit route for favicon at the root path
app.get('/cover-photo-tutorial-one.png', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'cover-photo-tutorial-one.png'));
});

// Explicit route for favicon at the root path
app.get('/rapid/tutorial-one-code', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'tutorials-one.html'));
});

// Explicit route for favicon at the root path
app.get('/byte-media-logo-v2.png', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'byte-media-logo-v2.png'));
});

// Assets
// Explicit route for favicon at the root path
app.get('/aa.svg', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'assets', 'AA.svg'));
});
// End Assets

// Explicit route for curl input
app.get('/rapid/curl', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'curl_input.html'));
});

// Explicit route for property inputs
app.get('/rapid/property-test', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'property.html'));
});

// RAPID API v1
/*
// Load RapidAPI proxy secret from environment variablesss
const validProxySecret = process.env.RAPIDAPI_PROXY_SECRET;

// Middleware to validate RapidAPI proxy secret for all requests except /apps/curl and /rapid/tutorial-one-code
app.use((req, res, next) => {
    if (req.path === '/rapid/curl' || req.path === '/rapid/tutorial-one-code') {
        return next();
    }

    const proxySecret = req.headers['x-rapidapi-proxy-secret'];
    if (!proxySecret || proxySecret !== validProxySecret) {
        console.warn("Invalid proxy secret:", proxySecret);
        return res.status(403).json({ error: 'Forbidden Access. Contact: <EMAIL>' });
    }
    next();
});

// Use CORS middleware (after proxy secret validation)
app.use(cors()); 
// Middleware for parsing JSON bodies
app.use(express.json());

// Serve static files from the 'public' directory (no authentication)
app.use(express.static(path.join(__dirname, 'public'))); 
*/
// END RAPID API

function organizeJson(obj) {
    if (Array.isArray(obj)) {
        const cleanedArray = obj
            .map(organizeJson)
            .filter(item => {
                if (Array.isArray(item)) return item.length > 0;
                if (item && typeof item === 'object') return Object.keys(item).length > 0;
                return item !== undefined && item !== '';  // Allow null values
            });

        return cleanedArray.length === 0 ? null : cleanedArray.length === 1 ? cleanedArray[0] : cleanedArray;
    } else if (obj !== null && typeof obj === 'object') {
        const newObj = Object.keys(obj)
            .reduce((acc, key) => {
                const value = organizeJson(obj[key]);
                if (value !== undefined && value !== '' && !(Array.isArray(value) && value.length === 0)) {
                    acc[key] = value;
                } else {
                    acc[key] = null;  // Replace empty values with null
                }
                return acc;
            }, {});
        return Object.keys(newObj).length > 0 ? newObj : null;  // Allow null values
    }
    return obj;
}

// Utility function to clean JSON data: Parameter True / False For Removing NULL Fields
function cleanJson(obj) {
    if (Array.isArray(obj)) {
        const cleanedArray = obj
            .map(cleanJson)
            .filter(item => {
                if (Array.isArray(item)) return item.length > 0;
                if (item && typeof item === 'object') return Object.keys(item).length > 0;
                return item !== null && item !== undefined && item !== '';
            });
        return cleanedArray.length === 1 ? cleanedArray[0] : cleanedArray;
    } else if (obj !== null && typeof obj === 'object') {
        const newObj = Object.keys(obj)
            .filter(key => obj[key] !== null && obj[key] !== undefined && obj[key] !== '' && !(Array.isArray(obj[key]) && obj[key].length === 0))
            .reduce((acc, key) => {
                const value = cleanJson(obj[key]);
                if (value !== null && value !== undefined && value !== '' && !(Array.isArray(value) && value.length === 0)) {
                    acc[key] = value;
                }
                return acc;
            }, {});
        return Object.keys(newObj).length > 0 ? newObj : null;
    }
    return obj;
}

// Reusable function to handle Go process output and errors
function handleGoProcessOutput(goProcess, res, clean, listingPhotos) {
    let output = '';
    let errorOutput = '';

    goProcess.stdout.on('data', (data) => {
        output += data;
    });

    goProcess.stderr.on('data', (data) => {
        errorOutput += data;
    });

    goProcess.on('close', (code) => {
        if (code !== 0) {
            console.error(`Error from Go process: ${errorOutput}`);
            return res.status(500).json({ error: 'Failed to retrieve property details' });
        }
        try {
            let jsonResponse = JSON.parse(output);

            if (clean === '1' || clean === 'true') {  // Ensure strict equality
                jsonResponse = cleanJson(jsonResponse);
            } else {
                jsonResponse = organizeJson(jsonResponse);
            }

            if (listingPhotos === '0' || listingPhotos === 'false') {
                if (jsonResponse) {
                    delete jsonResponse.responsivePhotos;
                }
            } else if (listingPhotos === '1' || listingPhotos === 'true') {
                if (jsonResponse) {
                    if (!jsonResponse.responsivePhotos) {
                        jsonResponse.responsivePhotos = [];
                    }
                }
            } 

            res.json(jsonResponse);
        } catch (parseError) {
            console.error(`Error parsing JSON: ${parseError}`);
            res.status(500).json({ error: 'Failed to parse JSON response' });
        }
    });
}

// Single Endpoint for Zillow Scraping
app.get('/property', (req, res) => {
    const zillowUrl = req.query.zillow_url;
    const zpid = req.query.zpid;
    const address = req.query.address;
    const clean = req.query.clean;
    const listingPhotos = req.query.listingPhotos;

    if (!['0', '1', 'true', 'false'].includes(clean)) {
        return res.status(400).json({ error: 'You must provide the clean parameter with value true or false. True cleans output and removes empty data lines. False shows all empty output data lines. Example: &clean=true&listingPhotos=false' });
    }

    if (!['0', '1', 'true', 'false'].includes(listingPhotos)) {
        return res.status(400).json({ error: 'You must provide the listingPhotos parameter with value false or true. false = Hide Listing Photos. True = Show Photos. Example: &clean=true&listingPhotos=true' });
    }

    let goProcess;

    if (zillowUrl) {
        if (!validator.isURL(zillowUrl)) {
            return res.status(400).json({ error: 'Invalid Zillow URL' });
        }
        goProcess = spawn(path.join(__dirname, 'bin', 'fl'), [zillowUrl]);
    } else if (zpid) {
        goProcess = spawn(path.join(__dirname, 'bin', 'propertyid'), [zpid]);
    } else if (address) {
        const sanitizedAddress = address.replace(/\s+/g, '-').replace(/[+]/g, '-');
        goProcess = spawn(path.join(__dirname, 'bin', 'propertyaddress'), [sanitizedAddress]);
    } else {
        return res.status(400).json({ error: 'You must provide either a Zillow URL, ZPID, or Address' });
    }

    handleGoProcessOutput(goProcess, res, clean, listingPhotos);
});
// Endpoint for property by address
app.get('/propertyAddress', (req, res) => {
    const address = req.query.address;
    const clean = req.query.clean;
    const listingPhotos = req.query.listingPhotos;

    if (!address) {
        return res.status(400).json({ error: 'You must provide an Address' });
    }
    if (!['0', '1', 'true', 'false'].includes(clean)) {
        return res.status(400).json({ error: 'You must provide the clean parameter with value false or true.' });
    }
    if (!['0', '1', 'true', 'false'].includes(listingPhotos)) {
        return res.status(400).json({ error: 'You must provide the listingPhotos parameter with value false or true.' });
    }

    const sanitizedAddress = address.replace(/\s+/g, '-').replace(/[+]/g, '-');
    const goProcess = spawn(path.join(__dirname, 'bin', 'propertyaddress'), [sanitizedAddress]);
    handleGoProcessOutput(goProcess, res, clean, listingPhotos);
});

// Endpoint for property by ZPID
app.get('/propertyZPID', (req, res) => {
    const zpid = req.query.zpid;
    const clean = req.query.clean;
    const listingPhotos = req.query.listingPhotos;

    if (!zpid) {
        return res.status(400).json({ error: 'You must provide a ZPID' });
    }
    if (!['0', '1', 'true', 'false'].includes(clean)) {
        return res.status(400).json({ error: 'You must provide the clean parameter with value true or false.' });
    }
    if (!['0', '1', 'true', 'false'].includes(listingPhotos)) {
        return res.status(400).json({ error: 'You must provide the listingPhotos parameter with value false or true.' });
    }

    const goProcess = spawn(path.join(__dirname, 'bin', 'propertyid'), [zpid]);
    handleGoProcessOutput(goProcess, res, clean, listingPhotos);
});

// Endpoint for property by Zillow URL
app.get('/propertyZillowURL', (req, res) => {
    const zillowUrl = req.query.zillow_url;
    const clean = req.query.clean;
    const listingPhotos = req.query.listingPhotos;

    if (!zillowUrl) {
        return res.status(400).json({ error: 'You must provide a Zillow URL' });
    }
    if (!validator.isURL(zillowUrl)) {
        return res.status(400).json({ error: 'Invalid Zillow URL' });
    }
    if (!['0', '1', 'true', 'false'].includes(clean)) {
        return res.status(400).json({ error: 'You must provide the clean parameter with value true or false.' });
    }
    if (!['0', '1', 'true', 'false'].includes(listingPhotos)) {
        return res.status(400).json({ error: 'You must provide the listingPhotos parameter with value false or true.' });
    }

    const goProcess = spawn(path.join(__dirname, 'bin', 'fl'), [zillowUrl]);
    handleGoProcessOutput(goProcess, res, clean, listingPhotos);
});

/* Search Zillow */
// Endpoint for Search Properties for Sale
app.get('/SearchForSale', (req, res) => {
    const city = req.query.city;
    const state = req.query.state;

    if (!city || !state) {
        return res.status(400).json({ error: 'You must provide both city and state' });
    }

    const goProcess = spawn(path.join(__dirname, 'bin', 'searchforsale'), [city, state]);
    handleGoProcessOutput(goProcess, res, 'true', 'false');
});
/* End Zillow Search */

// New endpoint for curl input
app.post('/rapid/curl', async (req, res) => {
    const { curlCommand } = req.body;
    try {
        const { stdout } = await execPromise(curlCommand);
        res.json(JSON.parse(stdout));
    } catch (error) {
        res.status(500).json({ error: 'Failed to execute curl command', details: error.message });
    }
});

const execPromise = (cmd) => {
    const exec = require('child_process').exec;
    return new Promise((resolve, reject) => {
        exec(cmd, (error, stdout, stderr) => {
            if (error) {
                reject(stderr);
            } else {
                resolve({ stdout, stderr });
            }
        });
    });
};

app.listen(port, () => {
    console.log(`Server is running on port ${port}`);
});
