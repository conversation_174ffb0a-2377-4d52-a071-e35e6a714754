# PropBolt API Documentation

## Overview

PropBolt is a comprehensive real estate data API that provides property details, search functionality, rent estimates, and autocomplete suggestions. This enhanced version includes improved reliability with retry logic, better error handling, and location-based search capabilities.

## Base URL
```
https://your-api-domain.com
```

## Authentication
All endpoints require RapidAPI proxy authentication unless running in development mode with `DISABLE_RAPIDAPI_PROXY_VALIDATION=true`.

## Enhanced Features

### 🔄 Automatic Retry Logic
- All endpoints include intelligent retry mechanisms
- Automatic retry on 403 errors, timeouts, and network issues
- Exponential backoff with configurable delays
- Random user agent rotation to avoid detection

### 🎯 Improved Error Handling
- Structured error responses with detailed information
- Better categorization of error types
- Retry attempt information in responses

### 🔍 Location-Based Search
- Search by address, Zillow URL, or property ID
- Automatic coordinate extraction and boundary calculation
- Enhanced search capabilities with location intelligence

## Endpoints

### 1. Property Details

#### GET `/property`
Retrieve comprehensive property information using ID, URL, or address.

**Parameters:**
- `id` (optional): Zillow property ID (zpid)
- `url` (optional): Full Zillow property URL
- `address` (optional): Property address
- `listingPhotos` (optional): Include photos (true/false, default: false)

**Example Requests:**

```bash
# By Property ID
curl "https://your-api-domain.com/property?id=12345678&listingPhotos=true"

# By Property URL
curl "https://your-api-domain.com/property?url=https://www.zillow.com/homedetails/123-Main-St/12345678_zpid/"

# By Address
curl "https://your-api-domain.com/property?address=123 Main St, City, State 12345"
```

**Response Example:**
```json
{
  "zpid": 12345678,
  "address": {
    "streetAddress": "123 Main St",
    "city": "Example City",
    "state": "CA",
    "zipcode": "12345"
  },
  "price": {
    "value": 750000,
    "currency": "USD"
  },
  "bedrooms": 3,
  "bathrooms": 2.5,
  "livingArea": 2100,
  "yearBuilt": 1995,
  "responsivePhotos": [
    {
      "url": "https://photos.zillowstatic.com/...",
      "width": 1024,
      "height": 768
    }
  ]
}
```

#### GET `/propertyMinimal`
Get essential property information with reduced data payload.

**Parameters:** Same as `/property`

#### GET `/propertyImages`
Retrieve only property images.

**Parameters:** Same as `/property` (excluding `listingPhotos`)

### 2. Search Properties

#### GET `/search/for-sale`
Search for properties currently for sale using geographic coordinates.

**Required Parameters:**
- `neLat`: Northeast latitude boundary
- `neLong`: Northeast longitude boundary  
- `swLat`: Southwest latitude boundary
- `swLong`: Southwest longitude boundary

**Optional Parameters:**
- `page`: Page number (default: 1)
- `zoom`: Map zoom level 1-20 (default: 10)
- `isAllHomes`: Include all home types (default: true)
- `isTownhouse`: Include townhouses (default: false)
- `isMultiFamily`: Include multi-family (default: false)
- `isCondo`: Include condos (default: false)
- `isLotLand`: Include lots/land (default: false)
- `isApartment`: Include apartments (default: false)
- `isManufactured`: Include manufactured homes (default: false)
- `isApartmentOrCondo`: Include apartments or condos (default: false)
- `priceMin`: Minimum price
- `priceMax`: Maximum price
- `monthlyPaymentMin`: Minimum monthly payment
- `monthlyPaymentMax`: Maximum monthly payment

**School Filters:**
- `isElementarySchool`: Filter by elementary schools
- `isMiddleSchool`: Filter by middle schools
- `isHighSchool`: Filter by high schools
- `isPublicSchool`: Filter by public schools
- `isPrivateSchool`: Filter by private schools
- `isCharterSchool`: Filter by charter schools
- `includeUnratedSchools`: Include unrated schools

#### 🆕 GET `/search/for-sale-by-location`
Search for properties for sale using address, Zillow URL, or property ID.

**Required Parameters (one of):**
- `address`: Property address or location
- `url`: Zillow property URL
- `id`: Zillow property ID (zpid)

**Optional Parameters:** Same as `/search/for-sale` (excluding coordinate parameters)

**Example:**
```bash
# Search by address
curl "https://your-api-domain.com/search/for-sale-by-location?address=123 Main St, New York, NY&priceMin=500000&priceMax=1000000"

# Search by Zillow URL
curl "https://your-api-domain.com/search/for-sale-by-location?url=https://www.zillow.com/homedetails/123-Main-St/12345678_zpid/&isTownhouse=true"

# Search by property ID
curl "https://your-api-domain.com/search/for-sale-by-location?id=12345678&zoom=15"
```

#### GET `/search/for-rent`
Search for rental properties using geographic coordinates.

**Required Parameters:**
- `neLat`, `neLong`, `swLat`, `swLong`: Geographic boundaries

**Optional Parameters:**
- `page`: Page number (default: 1)
- `zoom`: Map zoom level 1-20 (default: 10)

#### 🆕 GET `/search/for-rent-by-location`
Search for rental properties using address, Zillow URL, or property ID.

**Required Parameters (one of):**
- `address`: Property address or location
- `url`: Zillow property URL
- `id`: Zillow property ID (zpid)

**Optional Parameters:** Same as `/search/for-rent` (excluding coordinate parameters)

#### GET `/search/sold`
Search for recently sold properties using geographic coordinates.

**Parameters:** Same as `/search/for-rent`

#### 🆕 GET `/search/sold-by-location`
Search for recently sold properties using address, Zillow URL, or property ID.

**Required Parameters (one of):**
- `address`: Property address or location
- `url`: Zillow property URL
- `id`: Zillow property ID (zpid)

**Optional Parameters:** Same as `/search/sold` (excluding coordinate parameters)

**Search Response Format:**
```json
{
  "listResults": [
    {
      "zpid": 12345678,
      "address": "123 Main St",
      "price": 750000,
      "bedrooms": 3,
      "bathrooms": 2,
      "livingArea": 2100,
      "latitude": 40.7589,
      "longitude": -73.9851
    }
  ],
  "mapResults": [
    {
      "zpid": 12345678,
      "latitude": 40.7589,
      "longitude": -73.9851,
      "price": 750000
    }
  ],
  "totalCount": 1
}
```

### 3. Rent Estimates

#### GET `/rentEstimate`
Get rental price estimates for a property.

**Required Parameters:**
- `address`: Property address

**Optional Parameters:**
- `compPropStatus`: Comparable property status (true/false)
- `distanceInMiles`: Search radius in miles (default: 5)

**Example:**
```bash
curl "https://your-api-domain.com/rentEstimate?address=123 Main St, City, State&distanceInMiles=3"
```

**Response:**
```json
{
  "rentEstimate": {
    "amount": 3200,
    "currency": "USD"
  },
  "rentRange": {
    "low": 2800,
    "high": 3600
  },
  "comparableProperties": [
    {
      "address": "456 Oak St",
      "rent": 3100,
      "distance": 0.3
    }
  ]
}
```

### 4. Autocomplete & Suggestions

#### GET `/autocomplete`
Get address and location suggestions.

**Required Parameters:**
- `q`: Search query (address, city, neighborhood, etc.)

**Example:**
```bash
curl "https://your-api-domain.com/autocomplete?q=123 Main"
```

**Response:**
```json
{
  "results": [
    {
      "id": "suggestion-1",
      "regionId": 12345,
      "subType": "address",
      "display": "123 Main St, Example City, CA",
      "type": "SearchAssistanceRegionResult"
    }
  ],
  "requestId": "abc-123"
}
```

#### GET `/region-details`
Get detailed information and boundaries for a specific region.

**Required Parameters:**
- `regionId`: Region ID from autocomplete results

**Example:**
```bash
curl "https://your-api-domain.com/region-details?regionId=12345"
```

**Response:**
```json
{
  "regionInfo": [
    {
      "regionId": 12345,
      "regionType": 6,
      "regionName": "Example City",
      "displayName": "Example City, CA",
      "isPointRegion": false
    }
  ],
  "regionBounds": {
    "north": 40.7829,
    "south": 40.7489,
    "east": -73.9441,
    "west": -73.9965
  },
  "requestId": 123
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "message": "Property not found",
    "code": "PROPERTY_NOT_FOUND",
    "details": {
      "attempts": 3,
      "totalTime": "2.5s",
      "lastError": "status: 404"
    }
  }
}
```

### Common Error Codes
- `400`: Bad Request - Invalid parameters
- `403`: Forbidden - Authentication required
- `404`: Not Found - Resource not found
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server error
- `503`: Service Unavailable - Temporary service issue

### Retry Information
Enhanced endpoints provide retry information in error responses:
- `attempts`: Number of retry attempts made
- `totalTime`: Total time spent on retries
- `lastError`: Details of the final error

## Rate Limiting

- Standard rate limits apply per RapidAPI subscription
- Enhanced retry logic helps handle temporary rate limits
- Automatic backoff reduces likelihood of hitting limits

## Best Practices

### 1. Use Appropriate Endpoints
- Use `/propertyMinimal` for basic information
- Use `/propertyImages` when only photos are needed
- Use location-based search endpoints when you have address/URL/ID
- Use coordinate-based search for geographic area searches

### 2. Handle Geographic Searches
- Use location-based endpoints for property-specific searches
- Use coordinate-based endpoints for area-based searches
- Keep zoom levels between 10-15 for optimal results
- Use pagination for large result sets

### 3. Error Handling
- Implement proper error handling for all status codes
- Use retry information to adjust client-side retry logic
- Monitor rate limits and implement client-side throttling

### 4. Performance Optimization
- Cache autocomplete results for common queries
- Use minimal endpoints when full data isn't needed
- Implement client-side caching for property details

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const propboltAPI = {
  baseURL: 'https://your-api-domain.com',
  
  async getProperty(id, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/property`, {
        params: { id, ...options }
      });
      return response.data;
    } catch (error) {
      console.error('API Error:', error.response?.data);
      throw error;
    }
  },
  
  async searchForSaleByLocation(location, filters = {}) {
    const params = { ...location, ...filters };
    const response = await axios.get(`${this.baseURL}/search/for-sale-by-location`, { params });
    return response.data;
  },
  
  async autocomplete(query) {
    const response = await axios.get(`${this.baseURL}/autocomplete`, {
      params: { q: query }
    });
    return response.data;
  }
};

// Usage
(async () => {
  try {
    // Search by address
    const results = await propboltAPI.searchForSaleByLocation(
      { address: '123 Main St, New York, NY' },
      { priceMin: 500000, priceMax: 1000000 }
    );
    console.log('Search results:', results);
    
    // Search by property ID
    const propertyResults = await propboltAPI.searchForSaleByLocation(
      { id: 12345678 },
      { zoom: 15 }
    );
    console.log('Property area results:', propertyResults);
    
    const suggestions = await propboltAPI.autocomplete('123 Main St');
    console.log('Suggestions:', suggestions);
  } catch (error) {
    console.error('Error:', error);
  }
})();
```

### Python
```python
import requests

class PropBoltAPI:
    def __init__(self, base_url="https://your-api-domain.com"):
        self.base_url = base_url
    
    def get_property(self, property_id=None, url=None, address=None, listing_photos=False):
        params = {"listingPhotos": listing_photos}
        if property_id:
            params["id"] = property_id
        elif url:
            params["url"] = url
        elif address:
            params["address"] = address
        else:
            raise ValueError("Must provide property_id, url, or address")
        
        response = requests.get(f"{self.base_url}/property", params=params)
        response.raise_for_status()
        return response.json()
    
    def search_for_sale_by_location(self, address=None, url=None, property_id=None, **filters):
        params = filters.copy()
        if address:
            params["address"] = address
        elif url:
            params["url"] = url
        elif property_id:
            params["id"] = property_id
        else:
            raise ValueError("Must provide address, url, or property_id")
        
        response = requests.get(f"{self.base_url}/search/for-sale-by-location", params=params)
        response.raise_for_status()
        return response.json()
    
    def autocomplete(self, query):
        response = requests.get(f"{self.base_url}/autocomplete", params={"q": query})
        response.raise_for_status()
        return response.json()

# Usage
api = PropBoltAPI()

try:
    # Search by address
    search_results = api.search_for_sale_by_location(
        address="123 Main St, New York, NY",
        price_min=500000,
        price_max=1000000,
        is_townhouse=True
    )
    print("Search results:", len(search_results["listResults"]))
    
    # Search by Zillow URL
    url_results = api.search_for_sale_by_location(
        url="https://www.zillow.com/homedetails/123-Main-St/12345678_zpid/",
        zoom=15
    )
    print("URL search results:", len(url_results["listResults"]))
    
    # Search by property ID
    id_results = api.search_for_sale_by_location(
        property_id=12345678,
        zoom=12
    )
    print("ID search results:", len(id_results["listResults"]))
    
    suggestions = api.autocomplete("123 Main St")
    print("Suggestions:", suggestions)
    
except requests.exceptions.RequestException as e:
    print("API Error:", e)
```

## Changelog

### Enhanced Edition Features
- ✅ Automatic retry logic with exponential backoff
- ✅ Random user agent rotation
- ✅ Enhanced HTTP client configuration
- ✅ New autocomplete endpoints
- ✅ Region details functionality
- ✅ 🆕 Location-based search endpoints
- ✅ Improved error handling and reporting
- ✅ Better connection pooling and timeouts
- ✅ Structured retry information in responses

### Migration from Previous Version
- All existing endpoints remain compatible
- New optional location-based search endpoints available
- Enhanced error responses provide more information
- New autocomplete endpoints available immediately

## Support

For technical support, feature requests, or bug reports, please contact: <EMAIL>
