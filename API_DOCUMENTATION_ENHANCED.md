# PropBolt API Documentation - Enhanced Version

## Overview

PropBolt is a comprehensive real estate data API that provides property details, search functionality, rent estimates, and autocomplete suggestions. This enhanced version includes improved reliability with retry logic, better error handling, and new features inspired by the gozillow library.

## Base URL
```
https://your-api-domain.com
```

## Authentication
All endpoints require RapidAPI proxy authentication unless running in development mode with `DISABLE_RAPIDAPI_PROXY_VALIDATION=true`.

## Enhanced Features

### 🔄 Automatic Retry Logic
- All endpoints now include intelligent retry mechanisms
- Automatic retry on 403 errors, timeouts, and network issues
- Exponential backoff with configurable delays
- Random user agent rotation to avoid detection

### 🎯 Improved Error Handling
- Structured error responses with detailed information
- Better categorization of error types
- Retry attempt information in responses

### 🔍 New Autocomplete Features
- Address and location suggestions
- Region boundary detection
- Enhanced search capabilities

## Endpoints

### 1. Property Details

#### GET `/property`
Retrieve comprehensive property information using ID, URL, or address.

**Parameters:**
- `id` (optional): Zillow property ID (zpid)
- `url` (optional): Full Zillow property URL
- `address` (optional): Property address
- `listingPhotos` (optional): Include photos (true/false, default: false)

**Example Requests:**

```bash
# By Property ID
curl "https://your-api-domain.com/property?id=12345678&listingPhotos=true"

# By Property URL
curl "https://your-api-domain.com/property?url=https://www.zillow.com/homedetails/123-Main-St/12345678_zpid/"

# By Address
curl "https://your-api-domain.com/property?address=123 Main St, City, State 12345"
```

**Response Example:**
```json
{
  "zpid": 12345678,
  "address": {
    "streetAddress": "123 Main St",
    "city": "Example City",
    "state": "CA",
    "zipcode": "12345"
  },
  "price": {
    "value": 750000,
    "currency": "USD"
  },
  "bedrooms": 3,
  "bathrooms": 2.5,
  "livingArea": 2100,
  "yearBuilt": 1995,
  "responsivePhotos": [
    {
      "url": "https://photos.zillowstatic.com/...",
      "width": 1024,
      "height": 768
    }
  ]
}
```

#### GET `/propertyMinimal`
Get essential property information with reduced data payload.

**Parameters:** Same as `/property`

**Example:**
```bash
curl "https://your-api-domain.com/propertyMinimal?id=12345678"
```

#### GET `/propertyImages`
Retrieve only property images.

**Parameters:** Same as `/property` (excluding `listingPhotos`)

**Example:**
```bash
curl "https://your-api-domain.com/propertyImages?address=123 Main St, City, State"
```

### 2. Search Properties

#### GET `/search/for-sale`
Search for properties currently for sale.

**Required Parameters:**
- `neLat`: Northeast latitude boundary
- `neLong`: Northeast longitude boundary  
- `swLat`: Southwest latitude boundary
- `swLong`: Southwest longitude boundary

**Optional Parameters:**
- `page`: Page number (default: 1)
- `zoom`: Map zoom level 1-20 (default: 10)
- `isAllHomes`: Include all home types (default: true)
- `isTownhouse`: Include townhouses (default: false)
- `isMultiFamily`: Include multi-family (default: false)
- `isCondo`: Include condos (default: false)
- `isLotLand`: Include lots/land (default: false)
- `isApartment`: Include apartments (default: false)
- `isManufactured`: Include manufactured homes (default: false)
- `isApartmentOrCondo`: Include apartments or condos (default: false)
- `priceMin`: Minimum price
- `priceMax`: Maximum price
- `monthlyPaymentMin`: Minimum monthly payment
- `monthlyPaymentMax`: Maximum monthly payment

**School Filters:**
- `isElementarySchool`: Filter by elementary schools
- `isMiddleSchool`: Filter by middle schools
- `isHighSchool`: Filter by high schools
- `isPublicSchool`: Filter by public schools
- `isPrivateSchool`: Filter by private schools
- `isCharterSchool`: Filter by charter schools
- `includeUnratedSchools`: Include unrated schools

**Example:**
```bash
curl "https://your-api-domain.com/search/for-sale?neLat=40.7829&neLong=-73.9441&swLat=40.7489&swLong=-73.9965&priceMin=500000&priceMax=1000000&zoom=12"
```

#### GET `/search/for-rent`
Search for rental properties.

**Required Parameters:**
- `neLat`, `neLong`, `swLat`, `swLong`: Geographic boundaries

**Optional Parameters:**
- `page`: Page number (default: 1)
- `zoom`: Map zoom level 1-20 (default: 10)

**Example:**
```bash
curl "https://your-api-domain.com/search/for-rent?neLat=40.7829&neLong=-73.9441&swLat=40.7489&swLong=-73.9965"
```

#### GET `/search/sold`
Search for recently sold properties.

**Parameters:** Same as `/search/for-rent`

**Example:**
```bash
curl "https://your-api-domain.com/search/sold?neLat=40.7829&neLong=-73.9441&swLat=40.7489&swLong=-73.9965&zoom=15"
```

**Search Response Format:**
```json
{
  "listResults": [
    {
      "zpid": 12345678,
      "address": "123 Main St",
      "price": 750000,
      "bedrooms": 3,
      "bathrooms": 2,
      "livingArea": 2100,
      "latitude": 40.7589,
      "longitude": -73.9851
    }
  ],
  "mapResults": [
    {
      "zpid": 12345678,
      "latitude": 40.7589,
      "longitude": -73.9851,
      "price": 750000
    }
  ],
  "totalCount": 1
}
```

### 3. Rent Estimates

#### GET `/rentEstimate`
Get rental price estimates for a property.

**Required Parameters:**
- `address`: Property address

**Optional Parameters:**
- `compPropStatus`: Comparable property status (true/false)
- `distanceInMiles`: Search radius in miles (default: 5)

**Example:**
```bash
curl "https://your-api-domain.com/rentEstimate?address=123 Main St, City, State&distanceInMiles=3"
```

**Response:**
```json
{
  "rentEstimate": {
    "amount": 3200,
    "currency": "USD"
  },
  "rentRange": {
    "low": 2800,
    "high": 3600
  },
  "comparableProperties": [
    {
      "address": "456 Oak St",
      "rent": 3100,
      "distance": 0.3
    }
  ]
}
```

### 4. Autocomplete & Suggestions

#### GET `/autocomplete`
Get address and location suggestions.

**Required Parameters:**
- `q`: Search query (address, city, neighborhood, etc.)

**Example:**
```bash
curl "https://your-api-domain.com/autocomplete?q=123 Main"
```

**Response:**
```json
{
  "results": [
    {
      "id": "suggestion-1",
      "regionId": 12345,
      "subType": "address",
      "display": "123 Main St, Example City, CA",
      "type": "SearchAssistanceRegionResult"
    }
  ],
  "requestId": "abc-123"
}
```

#### GET `/region-details`
Get detailed information and boundaries for a specific region.

**Required Parameters:**
- `regionId`: Region ID from autocomplete results

**Example:**
```bash
curl "https://your-api-domain.com/region-details?regionId=12345"
```

**Response:**
```json
{
  "regionInfo": [
    {
      "regionId": 12345,
      "regionType": 6,
      "regionName": "Example City",
      "displayName": "Example City, CA",
      "isPointRegion": false
    }
  ],
  "regionBounds": {
    "north": 40.7829,
    "south": 40.7489,
    "east": -73.9441,
    "west": -73.9965
  },
  "requestId": 123
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "message": "Property not found",
    "code": "PROPERTY_NOT_FOUND",
    "details": {
      "attempts": 3,
      "totalTime": "2.5s",
      "lastError": "status: 404"
    }
  }
}
```

### Common Error Codes
- `400`: Bad Request - Invalid parameters
- `403`: Forbidden - Authentication required
- `404`: Not Found - Resource not found
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server error
- `503`: Service Unavailable - Temporary service issue

### Retry Information
Enhanced endpoints provide retry information in error responses:
- `attempts`: Number of retry attempts made
- `totalTime`: Total time spent on retries
- `lastError`: Details of the final error

## Rate Limiting

- Standard rate limits apply per RapidAPI subscription
- Enhanced retry logic helps handle temporary rate limits
- Automatic backoff reduces likelihood of hitting limits

## Best Practices

### 1. Use Appropriate Endpoints
- Use `/propertyMinimal` for basic information
- Use `/propertyImages` when only photos are needed
- Use specific search endpoints for different property types

### 2. Handle Geographic Searches
- Keep zoom levels between 10-15 for optimal results
- Limit search areas to reasonable sizes
- Use pagination for large result sets

### 3. Error Handling
- Implement proper error handling for all status codes
- Use retry information to adjust client-side retry logic
- Monitor rate limits and implement client-side throttling

### 4. Performance Optimization
- Cache autocomplete results for common queries
- Use minimal endpoints when full data isn't needed
- Implement client-side caching for property details

## SDK Examples

### JavaScript/Node.js
```javascript
const axios = require('axios');

const propboltAPI = {
  baseURL: 'https://your-api-domain.com',
  
  async getProperty(id, options = {}) {
    try {
      const response = await axios.get(`${this.baseURL}/property`, {
        params: { id, ...options }
      });
      return response.data;
    } catch (error) {
      console.error('API Error:', error.response?.data);
      throw error;
    }
  },
  
  async searchForSale(bounds, filters = {}) {
    const params = { ...bounds, ...filters };
    const response = await axios.get(`${this.baseURL}/search/for-sale`, { params });
    return response.data;
  },
  
  async autocomplete(query) {
    const response = await axios.get(`${this.baseURL}/autocomplete`, {
      params: { q: query }
    });
    return response.data;
  }
};

// Usage
(async () => {
  try {
    const property = await propboltAPI.getProperty(12345678, { listingPhotos: true });
    console.log('Property:', property);
    
    const suggestions = await propboltAPI.autocomplete('123 Main St');
    console.log('Suggestions:', suggestions);
  } catch (error) {
    console.error('Error:', error);
  }
})();
```

### Python
```python
import requests

class PropBoltAPI:
    def __init__(self, base_url="https://your-api-domain.com"):
        self.base_url = base_url
    
    def get_property(self, property_id=None, url=None, address=None, listing_photos=False):
        params = {"listingPhotos": listing_photos}
        if property_id:
            params["id"] = property_id
        elif url:
            params["url"] = url
        elif address:
            params["address"] = address
        else:
            raise ValueError("Must provide property_id, url, or address")
        
        response = requests.get(f"{self.base_url}/property", params=params)
        response.raise_for_status()
        return response.json()
    
    def search_for_sale(self, ne_lat, ne_long, sw_lat, sw_long, **filters):
        params = {
            "neLat": ne_lat,
            "neLong": ne_long,
            "swLat": sw_lat,
            "swLong": sw_long,
            **filters
        }
        response = requests.get(f"{self.base_url}/search/for-sale", params=params)
        response.raise_for_status()
        return response.json()
    
    def autocomplete(self, query):
        response = requests.get(f"{self.base_url}/autocomplete", params={"q": query})
        response.raise_for_status()
        return response.json()

# Usage
api = PropBoltAPI()

try:
    property_data = api.get_property(property_id=12345678, listing_photos=True)
    print("Property:", property_data)
    
    search_results = api.search_for_sale(
        ne_lat=40.7829, ne_long=-73.9441,
        sw_lat=40.7489, sw_long=-73.9965,
        price_min=500000, price_max=1000000
    )
    print("Search results:", len(search_results["listResults"]))
    
    suggestions = api.autocomplete("123 Main St")
    print("Suggestions:", suggestions)
    
except requests.exceptions.RequestException as e:
    print("API Error:", e)
```

## Changelog

### Enhanced Version Features
- ✅ Automatic retry logic with exponential backoff
- ✅ Random user agent rotation
- ✅ Enhanced HTTP client configuration
- ✅ New autocomplete endpoints
- ✅ Region details functionality
- ✅ Improved error handling and reporting
- ✅ Better connection pooling and timeouts
- ✅ Structured retry information in responses

### Migration from Previous Version
- All existing endpoints remain compatible
- New optional retry parameters available
- Enhanced error responses provide more information
- New autocomplete endpoints available immediately

## Support

For technical support, feature requests, or bug reports, please contact: <EMAIL>
