# PropBolt API - Enhanced Edition

A comprehensive real estate data API built with Go that provides property details, search functionality, rent estimates, and autocomplete suggestions using Zillow data. This enhanced version includes improved reliability, retry logic, and new features inspired by the gozillow library.

## 🚀 Enhanced Features

### New in Enhanced Edition
- **🔄 Automatic Retry Logic**: Intelligent retry mechanisms with exponential backoff
- **🎭 User Agent Rotation**: Random user agent rotation to avoid detection
- **🎯 Enhanced Error Handling**: Detailed error responses with retry information
- **🔍 Autocomplete API**: Address and location suggestions
- **📍 Region Details**: Geographic boundary detection and region information
- **⚡ Optimized HTTP Client**: Better connection pooling and timeout handling
- **🛡️ Improved Reliability**: Better handling of rate limits and network issues

### Core Features
- **Property Details**: Get comprehensive property information by ID, URL, or address
- **Property Search**: Search for properties for sale, rent, or recently sold with advanced filtering
- **Rent Estimates**: Get rental price estimates for properties
- **Multiple Data Formats**: Full details, minimal info, or images-only responses
- **Proxy Support**: Built-in proxy support for all endpoints
- **RapidAPI Integration**: Ready for RapidAPI marketplace deployment

## 📚 API Endpoints

### Property Information
- `GET /property` - Get property details (by ID, URL, or address)
- `GET /propertyMinimal` - Get essential property information
- `GET /propertyImages` - Get property images only

### Search
- `GET /search/for-sale` - Search properties for sale
- `GET /search/for-rent` - Search rental properties
- `GET /search/sold` - Search recently sold properties

### Estimates
- `GET /rentEstimate` - Get rental price estimates

### 🆕 Autocomplete & Suggestions
- `GET /autocomplete` - Get address and location suggestions
- `GET /region-details` - Get region boundaries and information

### Utility
- `GET /` - Health check endpoint

## 🚀 Quick Start

### Prerequisites
- Go 1.22.3 or later
- Internet connection for data retrieval

### Installation & Running

1. **Clone the repository:**
   ```bash
   <NAME_EMAIL>:brycebayens/rapidapi.git
   cd rapidapi
   ```

2. **Install dependencies:**
   ```bash
   go mod tidy
   ```

3. **Build the application:**
   ```bash
   go build -o propbolt
   ```

4. **Run locally:**
   ```bash
   DISABLE_RAPIDAPI_PROXY_VALIDATION=true DISABLE_FORCE_SSL=true PORT=8080 ./propbolt
   ```

5. **Test the API:**
   ```bash
   # Health check
   curl "http://localhost:8080/"

   # Property details
   curl "http://localhost:8080/property?id=12345678"

   # Autocomplete
   curl "http://localhost:8080/autocomplete?q=123%20Main%20St"
   ```

## 💡 Usage Examples

### Enhanced Property Retrieval
```bash
# Get property with automatic retry logic
curl "http://localhost:8080/property?id=12345678&listingPhotos=true"

# Get property by address with enhanced error handling
curl "http://localhost:8080/property?address=123%20Main%20St%20City%20State%2012345"
```

### New Autocomplete Features
```bash
# Get address suggestions
curl "http://localhost:8080/autocomplete?q=123%20Main"

# Get region details and boundaries
curl "http://localhost:8080/region-details?regionId=12345"
```

### Enhanced Search with Filters
```bash
# Search with comprehensive filters
curl "http://localhost:8080/search/for-sale?neLat=40.7829&neLong=-73.9441&swLat=40.7489&swLong=-73.9965&priceMin=500000&priceMax=1000000&isTownhouse=true&isElementarySchool=true"
```

### Rent Estimates with Distance Control
```bash
# Get rent estimate with custom search radius
curl "http://localhost:8080/rentEstimate?address=123%20Main%20St%20City%20State&distanceInMiles=3&compPropStatus=true"
```

## 🔧 Environment Variables

### Required
- `PORT` - Server port number

### Optional (Development)
- `DISABLE_RAPIDAPI_PROXY_VALIDATION=true` - Disable API key validation
- `DISABLE_FORCE_SSL=true` - Disable SSL enforcement

### Production
- `RAPIDAPI_PROXY_SECRET` - RapidAPI proxy validation secret

## 🛠️ Enhanced Project Structure

```
propbolt/
├── main.go                 # Main server and HTTP handlers
├── details/               # Property details retrieval
│   ├── data.go           # Data fetching logic
│   ├── formatRaw1.go     # Data structures and parsing
│   ├── get.go            # 🔄 Enhanced with retry logic
│   └── parse.go          # HTML parsing utilities
├── zestimate/            # Rent estimation functionality
│   ├── execute.go        # Main execution logic
│   ├── rent_zestimate.go # Data structures
│   └── rent_zestimate_data.go # Data retrieval
├── search/               # Property search capabilities
│   ├── search.go         # 🔄 Enhanced with retry logic
│   ├── formatInputRaw.go # Request structures
│   ├── formatOutputRaw.go # Response structures
│   └── variables.go      # Constants
├── autocomplete/         # 🆕 Autocomplete functionality
│   └── autocomplete.go   # Address suggestions and region details
├── utils/                # 🔄 Enhanced utility functions
│   └── utils.go          # Retry logic, HTTP client, headers
├── propbolthelper/       # Client helper functions
│   ├── client.go         # Client interface
│   └── format.go         # Data formatting
└── public/               # Static files
    ├── tutorials-one.html
    ├── curl_input.html
    └── assets/
```

## 🔄 Enhanced Dependencies

```go
require (
    github.com/PuerkitoBio/goquery v1.9.2
    github.com/corpix/uarand v0.2.0      // 🆕 Random user agents
    github.com/google/uuid v1.6.0        // 🆕 UUID generation
)
```

## 📊 Enhanced Response Formats

### Property Details with Retry Information
```json
{
  "zpid": 12345678,
  "address": {
    "streetAddress": "123 Main St",
    "city": "Example City",
    "state": "CA",
    "zipcode": "12345"
  },
  "price": {
    "value": 750000,
    "currency": "USD"
  },
  "bedrooms": 3,
  "bathrooms": 2.5,
  "livingArea": 2100,
  "yearBuilt": 1995
}
```

### Autocomplete Response
```json
{
  "results": [
    {
      "id": "suggestion-1",
      "regionId": 12345,
      "subType": "address",
      "display": "123 Main St, Example City, CA",
      "type": "SearchAssistanceRegionResult"
    }
  ],
  "requestId": "abc-123"
}
```

### Enhanced Error Response
```json
{
  "error": {
    "message": "Property not found",
    "code": "PROPERTY_NOT_FOUND",
    "details": {
      "attempts": 3,
      "totalTime": "2.5s",
      "lastError": "status: 404"
    }
  }
}
```

## 🧪 Testing Enhanced Features

```bash
# Start server with enhanced logging
DISABLE_RAPIDAPI_PROXY_VALIDATION=true PORT=8080 ./propbolt

# Test retry logic (will automatically retry on failures)
curl "http://localhost:8080/property?id=12345678"

# Test autocomplete
curl "http://localhost:8080/autocomplete?q=New%20York"

# Test region details
curl "http://localhost:8080/region-details?regionId=61987"

# Test enhanced search
curl "http://localhost:8080/search/for-sale?neLat=40.7829&neLong=-73.9441&swLat=40.7489&swLong=-73.9965&zoom=12"
```

## 🔄 Migration from Previous Version

### Backward Compatibility
- ✅ All existing endpoints remain fully compatible
- ✅ No breaking changes to existing API contracts
- ✅ Enhanced error responses provide additional information

### New Features Available Immediately
- 🆕 `/autocomplete` endpoint for address suggestions
- 🆕 `/region-details` endpoint for geographic boundaries
- 🔄 Automatic retry logic on all existing endpoints
- 🎭 Random user agent rotation for better reliability
- 📊 Enhanced error responses with retry information

## 🚀 Performance Improvements

### Enhanced HTTP Client
- **Connection Pooling**: Optimized connection reuse
- **Timeout Handling**: Better timeout management
- **Retry Logic**: Intelligent retry with exponential backoff
- **User Agent Rotation**: Reduces detection and blocking

### Reliability Features
- **Automatic Retries**: Retry on 403, timeout, and network errors
- **Exponential Backoff**: Prevents overwhelming servers
- **Error Categorization**: Better error handling and reporting
- **Circuit Breaker Pattern**: Prevents cascade failures

## 📖 Comprehensive Documentation

For detailed API documentation with examples, see:
- [Enhanced API Documentation](./API_DOCUMENTATION_ENHANCED.md)

## 🐳 Production Deployment

### Build Commands
```bash
# Local development build
go build -o propbolt

# Production build (Linux)
GOOS=linux GOARCH=amd64 go build -o propbolt
```

### Docker Deployment
```dockerfile
FROM golang:1.22-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o propbolt

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/propbolt .
COPY --from=builder /app/public ./public
EXPOSE 8080
CMD ["./propbolt"]
```

### Heroku Deployment
The project includes a `Procfile` for Heroku deployment:
```
web: ./propbolt
```

### Environment Setup
```bash
export PORT=8080
export RAPIDAPI_PROXY_SECRET=your_secret_here
./propbolt
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Go best practices
- Add tests for new features
- Update documentation
- Ensure backward compatibility

## 📈 Changelog

### Enhanced Edition v2.0
- ✅ Added automatic retry logic with exponential backoff
- ✅ Implemented random user agent rotation
- ✅ Enhanced HTTP client configuration
- ✅ Added autocomplete endpoints
- ✅ Added region details functionality
- ✅ Improved error handling and reporting
- ✅ Better connection pooling and timeouts
- ✅ Structured retry information in responses

### Previous Version v1.0
- ✅ Basic property details, search, and rent estimates
- ✅ RapidAPI integration
- ✅ Proxy support

## 📄 License

This project is proprietary software developed by Byte Media.

## 🆘 Support

For technical support, feature requests, or bug reports:
- 📧 Email: <EMAIL>
- 📚 Documentation: [API_DOCUMENTATION_ENHANCED.md](./API_DOCUMENTATION_ENHANCED.md)
- 🐛 Issues: Create an issue in this repository

## 🙏 Acknowledgments

- Inspired by the [gozillow](https://github.com/johnbalvin/gozillow) library
- Enhanced with modern Go practices and reliability patterns
- Built for production-scale real estate data applications

## 📊 Version

Enhanced Edition v2.0 - Built with reliability and performance in mind
