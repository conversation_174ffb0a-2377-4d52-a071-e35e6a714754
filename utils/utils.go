package utils

import (
	"bytes"
	"math"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/corpix/uarand"
)
var regexSpace = regexp.MustCompile(`[\s ]+`)

func RemoveSpace(value string) string {
	return regexSpace.ReplaceAllString(strings.TrimSpace(value), " ")
}

func RemoveSpaceByte(value []byte) []byte {
	return regexSpace.ReplaceAll(bytes.TrimSpace(value), []byte(" "))
}

func ParseProxy(urlToParse, userName, password string) (*url.URL, error) {
	urlToUse, err := url.Parse(urlToParse)
	if err != nil {
		return nil, err
	}
	urlToUse.User = url.UserPassword(userName, password)
	return urlToUse, nil
}

// CreateHTTPClient creates an optimized HTTP client with proper configuration
func CreateHTTPClient(proxyURL *url.URL, timeout time.Duration) *http.Client {
	transport := &http.Transport{
		MaxIdleConnsPerHost: 30,
		DisableKeepAlives:   true,
	}
	if proxyURL != nil {
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	return &http.Client{
		Timeout: timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
		Transport: transport,
	}
}

// SetRealisticHeaders sets realistic browser headers with random user agent
func SetRealisticHeaders(req *http.Request) {
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "en")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Sec-Ch-Ua", `"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "none")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
	req.Header.Set("User-Agent", uarand.GetRandom())
}

// SetJSONHeaders sets headers for JSON API requests
func SetJSONHeaders(req *http.Request) {
	req.Header.Set("Accept", "*/*")
	req.Header.Set("Accept-Language", "en")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Sec-Ch-Ua", `"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Sec-Fetch-Dest", "empty")
	req.Header.Set("Sec-Fetch-Mode", "cors")
	req.Header.Set("Sec-Fetch-Site", "same-origin")
	req.Header.Set("User-Agent", uarand.GetRandom())
}

// RetryConfig holds configuration for retry logic
type RetryConfig struct {
	MaxRetries      int
	InitialDelay    time.Duration
	MaxDelay        time.Duration
	BackoffFactor   float64
	RetryOn403      bool
	RetryOnTimeout  bool
	RetryOnNetwork  bool
}

// DefaultRetryConfig returns a sensible default retry configuration
func DefaultRetryConfig() RetryConfig {
	return RetryConfig{
		MaxRetries:      3,
		InitialDelay:    1 * time.Second,
		MaxDelay:        30 * time.Second,
		BackoffFactor:   2.0,
		RetryOn403:      true,
		RetryOnTimeout:  true,
		RetryOnNetwork:  true,
	}
}

// RetryResult holds the result of a retry operation
type RetryResult struct {
	Success     bool
	Attempts    int
	LastError   error
	TotalTime   time.Duration
}

// ShouldRetry determines if an error should trigger a retry
func (rc RetryConfig) ShouldRetry(err error, attempt int) bool {
	if attempt >= rc.MaxRetries {
		return false
	}

	if err == nil {
		return false
	}

	errStr := err.Error()

	// Check for specific error conditions
	if rc.RetryOn403 && strings.Contains(errStr, "status: 403") {
		return true
	}

	if rc.RetryOnTimeout && (strings.Contains(errStr, "timeout") || strings.Contains(errStr, "deadline exceeded")) {
		return true
	}

	if rc.RetryOnNetwork && (strings.Contains(errStr, "connection") || strings.Contains(errStr, "network")) {
		return true
	}

	return false
}

// CalculateDelay calculates the delay for the next retry attempt
func (rc RetryConfig) CalculateDelay(attempt int) time.Duration {
	delay := time.Duration(float64(rc.InitialDelay) * math.Pow(rc.BackoffFactor, float64(attempt)))
	if delay > rc.MaxDelay {
		delay = rc.MaxDelay
	}
	return delay
}

// WithRetry executes a function with retry logic
func WithRetry[T any](fn func() (T, error), config RetryConfig) (T, RetryResult) {
	var result T
	var lastError error
	startTime := time.Now()

	for attempt := 0; attempt < config.MaxRetries; attempt++ {
		result, lastError = fn()

		if lastError == nil {
			return result, RetryResult{
				Success:   true,
				Attempts:  attempt + 1,
				LastError: nil,
				TotalTime: time.Since(startTime),
			}
		}

		if !config.ShouldRetry(lastError, attempt) {
			break
		}

		if attempt < config.MaxRetries-1 {
			delay := config.CalculateDelay(attempt)
			time.Sleep(delay)
		}
	}

	return result, RetryResult{
		Success:   false,
		Attempts:  config.MaxRetries,
		LastError: lastError,
		TotalTime: time.Since(startTime),
	}
}
